import axios from 'axios'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器（简化版）
request.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器（静默处理API错误）
request.interceptors.response.use(
  (response) => {
    const data = response.data

    // 如果是成功响应，直接返回数据
    if (data.status === 'success' || response.status === 200) {
      return data
    }

    // 如果是错误响应，只在控制台记录
    if (data.status === 'error') {
      console.warn('API响应错误:', data.error || '请求失败')
      return Promise.reject(new Error(data.error || '请求失败'))
    }

    return data
  },
  (error) => {
    // 静默处理错误，只在控制台记录
    console.log('API请求失败:', error.message)

    // 不显示错误消息，让调用方决定如何处理
    return Promise.reject(error)
  }
)

export default request
