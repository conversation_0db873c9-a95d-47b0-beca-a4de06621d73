<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <header class="dashboard-header">
      <div class="header-left">
        <div class="logo">
          <el-icon size="24" color="#4a6cf7">
            <Key />
          </el-icon>
          <span class="logo-text">Token管理系统</span>
        </div>
      </div>
      
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-dropdown">
            <el-icon>
              <User />
            </el-icon>
            <span>管理员</span>
            <el-icon>
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                设置
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="dashboard-main">
      <!-- 侧边栏 -->
      <aside class="dashboard-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-toggle">
          <el-button
            text
            @click="toggleSidebar"
            class="toggle-btn"
          >
            <el-icon>
              <Expand v-if="sidebarCollapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>
        </div>
        
        <nav class="sidebar-nav">
          <el-menu
            :default-active="$route.path"
            :collapse="sidebarCollapsed"
            router
            class="sidebar-menu"
          >
            <el-menu-item index="/dashboard/tokens">
              <el-icon><List /></el-icon>
              <span>Token列表</span>
            </el-menu-item>
            
            <el-menu-item index="/dashboard/add-token">
              <el-icon><Plus /></el-icon>
              <span>获取Token</span>
            </el-menu-item>
            
            <el-menu-item index="/dashboard/settings">
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </el-menu-item>
          </el-menu>
        </nav>
      </aside>

      <!-- 内容区域 -->
      <main class="dashboard-content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Key,
  User,
  ArrowDown,
  Setting,
  SwitchButton,
  Expand,
  Fold,
  List,
  Plus
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const sidebarCollapsed = ref(false)

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const handleCommand = async (command) => {
  switch (command) {
    case 'settings':
      router.push('/dashboard/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '确认退出',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await authStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}
</script>

<style scoped lang="scss">
.dashboard-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color-page);
}

.dashboard-header {
  height: 60px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
  
  .header-left {
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .logo-text {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .header-right {
    .settings-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.3s;

      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
  }
}

.dashboard-main {
  flex: 1;
  display: flex;
  min-height: calc(100vh - 60px);
}

.dashboard-sidebar {
  width: 240px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  position: relative;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-toggle {
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .toggle-btn {
      width: 100%;
      justify-content: center;
    }
  }
  
  .sidebar-nav {
    .sidebar-menu {
      border: none;
      
      :deep(.el-menu-item) {
        margin: 4px 8px;
        border-radius: 6px;
        
        &:hover {
          background-color: var(--el-fill-color-light);
        }
        
        &.is-active {
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
        }
      }
    }
  }
}

.dashboard-content {
  flex: 1;
  overflow: auto;
}

@media (max-width: 768px) {
  .dashboard-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 99;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &:not(.collapsed) {
      transform: translateX(0);
    }
  }
  
  .dashboard-content {
    margin-left: 0;
  }
}
</style>
