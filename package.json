{"name": "vue-token-manager", "version": "1.0.0", "description": "Vue.js Token管理系统", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview", "diagnose": "node diagnose.js"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@supabase/supabase-js": "^2.50.1", "axios": "^1.5.0", "dexie": "^3.2.4", "element-plus": "^2.3.9", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "sass": "^1.66.1", "vite": "^4.4.9"}, "keywords": ["vue", "token", "management", "indexeddb"], "author": "Your Name", "license": "MIT"}