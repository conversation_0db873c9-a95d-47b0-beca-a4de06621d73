{"name": "token-oauth-api", "version": "1.0.0", "description": "简化的Token OAuth API服务", "main": "src/app.js", "type": "module", "scripts": {"setup": "node setup.js", "dev": "nodemon src/app.js", "start": "node src/app.js", "test": "echo 'No tests specified'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.10.0", "axios": "^1.5.0", "crypto": "^1.0.1", "uuid": "^9.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["o<PERSON>h", "token", "api", "express", "augment"], "author": "Your Name", "license": "MIT"}