<template>
  <div class="add-token-container">
    <div class="page-title">
      <el-icon class="icon">
        <Plus />
      </el-icon>
      获取Token
    </div>

    <div class="steps-container">
      <!-- 步骤1: 获取授权地址 -->
      <div class="step-card">
        <div class="step-header">
          <div class="step-number">1</div>
          <h3>获取授权地址</h3>
        </div>
        
        <div class="step-content">
          <p>点击下方按钮获取授权地址，然后在浏览器中打开该地址进行授权。</p>

          <el-button
            type="primary"
            :loading="gettingAuthUrl"
            @click="getAuthUrl"
            size="large"
          >
            <el-icon>
              <Link />
            </el-icon>
            获取授权地址
          </el-button>

          <div v-if="authUrl" class="auth-url-container">
            <div class="auth-url-label">授权地址：</div>
            <div class="auth-url-display">
              <el-input
                :model-value="authUrl"
                readonly
                class="auth-url-input"
              >
                <template #append>
                  <el-button @click="copyAuthUrl">
                    <el-icon>
                      <DocumentCopy />
                    </el-icon>
                    复制
                  </el-button>
                </template>
              </el-input>
            </div>

            <div class="auth-url-actions">
              <el-button
                type="success"
                @click="openAuthUrl"
              >
                <el-icon>
                  <ChromeFilled />
                </el-icon>
                在浏览器中打开
              </el-button>
            </div>
          </div>

          <div v-if="!authUrl" class="manual-auth-info">
            <el-alert
              title="手动获取授权地址"
              type="info"
              :closable="false"
            >
              <p>如果自动获取失败，您可以手动构建授权地址：</p>
              <p><strong>格式：</strong> https://your-tenant.api.augmentcode.com/oauth/authorize?client_id=augment-api&response_type=code&redirect_uri=http://localhost:3000/callback</p>
            </el-alert>
          </div>
        </div>
      </div>

      <!-- 步骤2: 提交授权响应 -->
      <div class="step-card">
        <div class="step-header">
          <div class="step-number">2</div>
          <h3>提交授权响应</h3>
        </div>
        
        <div class="step-content">
          <p>完成授权后，将获得的授权响应粘贴到下面的文本框中：</p>
          
          <el-form
            ref="authFormRef"
            :model="authForm"
            :rules="authRules"
            @submit.prevent="submitAuth"
          >
            <el-form-item prop="response">
              <el-input
                v-model="authForm.response"
                type="textarea"
                :rows="6"
                placeholder='{"code":"_000baec407c57c4bf9xxxxxxxxxxxxxx","state":"0uXxxxxxxxx","tenant_url":"https://dxx.api.augmentcode.com/"}'
                class="auth-response-input"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                :loading="submittingAuth"
                @click="submitAuth"
                size="large"
              >
                <el-icon>
                  <Check />
                </el-icon>
                获取Token
              </el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="submitResult" class="submit-result">
            <el-alert
              :title="submitResult.success ? '获取成功' : '获取失败'"
              :type="submitResult.success ? 'success' : 'error'"
              :description="submitResult.message"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Link,
  DocumentCopy,
  ChromeFilled,
  Check
} from '@element-plus/icons-vue'
import { authApi } from '@/api/auth'
import { useTokenStore } from '@/stores/tokens'

const router = useRouter()
const tokenStore = useTokenStore()

// 响应式数据
const authUrl = ref('')
const gettingAuthUrl = ref(false)
const submittingAuth = ref(false)
const submitResult = ref(null)

const authFormRef = ref()

const authForm = reactive({
  response: ''
})

const authRules = {
  response: [
    { required: true, message: '请输入授权响应', trigger: 'blur' },
    { validator: validateAuthResponse, trigger: 'blur' }
  ]
}

// 验证授权响应格式
function validateAuthResponse(rule, value, callback) {
  if (!value) {
    callback()
    return
  }

  try {
    const parsed = JSON.parse(value)
    if (!parsed.code || !parsed.tenant_url) {
      callback(new Error('授权响应格式不正确，缺少必要字段'))
      return
    }
    callback()
  } catch (error) {
    callback(new Error('授权响应必须是有效的JSON格式'))
  }
}

// 获取授权地址
const getAuthUrl = async () => {
  gettingAuthUrl.value = true
  try {
    const response = await authApi.getAuthUrl()
    if (response.authorize_url) {
      authUrl.value = response.authorize_url
      ElMessage.success('授权地址获取成功')
    } else {
      ElMessage.error('获取授权地址失败')
    }
  } catch (error) {
    console.error('获取授权地址失败:', error)
    ElMessage.warning('无法连接到后端API，请手动构建授权地址')
    // 不设置authUrl，让用户看到手动获取的提示
  } finally {
    gettingAuthUrl.value = false
  }
}

// 复制授权地址
const copyAuthUrl = async () => {
  try {
    await navigator.clipboard.writeText(authUrl.value)
    ElMessage.success('授权地址已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 在浏览器中打开授权地址
const openAuthUrl = () => {
  if (authUrl.value) {
    window.open(authUrl.value, '_blank')
  }
}

// 提交授权响应
const submitAuth = async () => {
  if (!authFormRef.value) return

  try {
    const valid = await authFormRef.value.validate()
    if (!valid) return

    submittingAuth.value = true
    submitResult.value = null

    const authData = JSON.parse(authForm.response)

    try {
      // 尝试通过API获取token
      const response = await authApi.submitAuthCode(authData)

      if (response.token) {
        submitResult.value = {
          success: true,
          message: `Token获取成功: ${response.token.substring(0, 20)}...`
        }

        // 添加到本地存储
        await tokenStore.addTokenToLocal({
          token: response.token,
          tenant_url: authData.tenant_url
        })

        ElMessage.success('Token获取成功')

        // 3秒后跳转到Token列表
        setTimeout(() => {
          router.push('/dashboard/tokens')
        }, 3000)
      } else {
        submitResult.value = {
          success: false,
          message: response.error || '获取Token失败'
        }
      }
    } catch (apiError) {
      // API失败时，提供手动添加选项
      console.error('API获取Token失败:', apiError)
      submitResult.value = {
        success: false,
        message: '无法连接到后端API。如果您已经有Token，请使用下方的批量添加功能手动添加。'
      }
    }
  } catch (error) {
    console.error('提交授权失败:', error)
    submitResult.value = {
      success: false,
      message: error.message || '授权响应格式错误'
    }
  } finally {
    submittingAuth.value = false
  }
}


</script>

<style scoped lang="scss">
.add-token-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

.step-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: var(--el-fill-color-extra-light);
  border-bottom: 1px solid var(--el-border-color-lighter);

  .step-number {
    width: 32px;
    height: 32px;
    background: var(--el-color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
  }

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.step-content {
  padding: 24px;

  p {
    margin: 0 0 20px 0;
    color: var(--el-text-color-secondary);
    line-height: 1.6;
  }
}

.auth-url-container {
  margin-top: 20px;

  .auth-url-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--el-text-color-primary);
  }

  .auth-url-display {
    margin-bottom: 16px;

    .auth-url-input {
      :deep(.el-input__inner) {
        font-family: 'Courier New', monospace;
        font-size: 13px;
      }
    }
  }

  .auth-url-actions {
    display: flex;
    gap: 12px;
  }
}

.auth-response-input {
  :deep(.el-textarea__inner) {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
  }
}

.submit-result {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .add-token-container {
    padding: 12px;
  }

  .step-header {
    padding: 16px 20px;

    .step-number {
      width: 28px;
      height: 28px;
      font-size: 14px;
    }

    h3 {
      font-size: 16px;
    }
  }

  .step-content {
    padding: 20px;
  }

  .auth-url-actions {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
}
</style>
