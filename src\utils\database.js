import Dexie from 'dexie'

// 定义数据库
export class TokenDatabase extends <PERSON><PERSON> {
  constructor() {
    super('TokenManagerDB')
    
    // 定义数据库结构
    this.version(1).stores({
      // 认证信息表
      auth: '++id, token, user, createdAt',

      // Token信息表
      tokens: '++id, token, tenantUrl, remark, inCool, coolEnd, createdAt',

      // 设置信息表
      settings: '++id, key, value, updatedAt'
    })

    // 版本2：添加用量地址字段
    this.version(2).stores({
      // 认证信息表
      auth: '++id, token, user, createdAt',

      // Token信息表 - 添加usageUrl字段
      tokens: '++id, token, tenantUrl, remark, inCool, coolEnd, createdAt, usageUrl',

      // 设置信息表
      settings: '++id, key, value, updatedAt'
    })
  }
}

// 创建数据库实例
export const db = new TokenDatabase()

// 数据库工具函数
export const dbUtils = {
  // 清空所有数据
  async clearAll() {
    await db.auth.clear()
    await db.tokens.clear()
    await db.settings.clear()
  },
  
  // 导出数据
  async exportData() {
    const data = {
      auth: await db.auth.toArray(),
      tokens: await db.tokens.toArray(),
      settings: await db.settings.toArray(),
      exportTime: new Date().toISOString()
    }
    return data
  },
  
  // 导入数据
  async importData(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('无效的数据格式')
    }
    
    // 清空现有数据
    await this.clearAll()
    
    // 导入新数据
    if (data.auth && Array.isArray(data.auth)) {
      await db.auth.bulkAdd(data.auth)
    }
    
    if (data.tokens && Array.isArray(data.tokens)) {
      await db.tokens.bulkAdd(data.tokens)
    }
    
    if (data.settings && Array.isArray(data.settings)) {
      await db.settings.bulkAdd(data.settings)
    }
  },
  
  // 获取数据库统计信息
  async getStats() {
    const stats = {
      authCount: await db.auth.count(),
      tokensCount: await db.tokens.count(),
      settingsCount: await db.settings.count(),
      dbSize: await this.getDatabaseSize()
    }
    return stats
  },
  
  // 获取数据库大小（估算）
  async getDatabaseSize() {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate()
        return estimate.usage || 0
      }
      return 0
    } catch (error) {
      console.error('获取数据库大小失败:', error)
      return 0
    }
  }
}

// 设置相关的工具函数
export const settingsUtils = {
  // 获取设置
  async get(key, defaultValue = null) {
    try {
      const setting = await db.settings.where('key').equals(key).first()
      return setting ? setting.value : defaultValue
    } catch (error) {
      console.error('获取设置失败:', error)
      return defaultValue
    }
  },
  
  // 设置值
  async set(key, value) {
    try {
      const existing = await db.settings.where('key').equals(key).first()
      if (existing) {
        await db.settings.update(existing.id, {
          value,
          updatedAt: new Date()
        })
      } else {
        await db.settings.add({
          key,
          value,
          updatedAt: new Date()
        })
      }
    } catch (error) {
      console.error('设置值失败:', error)
      throw error
    }
  },
  
  // 删除设置
  async remove(key) {
    try {
      await db.settings.where('key').equals(key).delete()
    } catch (error) {
      console.error('删除设置失败:', error)
      throw error
    }
  },
  
  // 获取所有设置
  async getAll() {
    try {
      const settings = await db.settings.toArray()
      const result = {}
      settings.forEach(setting => {
        result[setting.key] = setting.value
      })
      return result
    } catch (error) {
      console.error('获取所有设置失败:', error)
      return {}
    }
  }
}
