<template>
  <div class="token-list-container">
    <div class="page-title">
      <el-icon class="icon">
        <List />
      </el-icon>
      Token列表
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索Token或备注..."
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="toolbar-right">
        <el-button
          type="primary"
          :icon="tokenVisible ? Hide : View"
          @click="toggleTokenVisibility"
        >
          {{ tokenVisible ? '隐藏Token' : '显示Token' }}
        </el-button>
        
        <el-button
          type="success"
          :icon="Refresh"
          :loading="tokenStore.isLoading"
          @click="refreshTokens"
        >
          刷新列表
        </el-button>
        

      </div>
    </div>

    <!-- 状态提示 -->
    <div v-if="showStatusInfo" class="status-info">
      <el-alert
        title="本地存储模式"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>当前使用本地存储模式，所有数据保存在浏览器中。</p>
          <p>如需OAuth功能，请启动Node.js OAuth API服务。</p>
        </template>
      </el-alert>
    </div>

    <!-- Token列表 -->
    <div class="token-list-content">
      <div v-if="tokenStore.isLoading" class="loading-container">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span class="loading-text">加载中...</span>
      </div>
      
      <div v-else-if="filteredTokens.length === 0" class="empty-state">
        <el-icon class="empty-icon">
          <DocumentRemove />
        </el-icon>
        <div class="empty-title">暂无Token数据</div>
        <div class="empty-description">
          {{ searchKeyword ? '没有找到匹配的Token' : '请先获取Token' }}
        </div>
      </div>
      
      <div v-else class="token-cards">
        <div
          v-for="(token, index) in paginatedTokens"
          :key="token.token"
          class="token-card"
        >
          <div class="token-header">
            <div class="token-number">{{ getTokenNumber(index) }}</div>
            <div class="token-info">
              <div class="token-value" :class="{ blurred: !tokenVisible }">
                {{ token.token }}
              </div>
              <div class="token-meta">
                <el-tag
                  v-if="token.remark"
                  size="small"
                  type="info"
                  class="remark-tag"
                  @click="editRemark(token)"
                >
                  {{ token.remark }}
                </el-tag>
                <el-tag
                  v-else
                  size="small"
                  type="info"
                  class="remark-tag empty"
                  @click="editRemark(token)"
                >
                  添加备注
                </el-tag>
                
                <el-tag
                  v-if="token.in_cool"
                  size="small"
                  type="warning"
                  class="cool-tag"
                >
                  <el-icon><Timer /></el-icon>
                  冷却中
                </el-tag>

                <!-- 创建时间标签 -->
                <el-tag
                  size="small"
                  type="info"
                  class="time-tag"
                >
                  <el-icon><Clock /></el-icon>
                  {{ formatCreateTime(token.createdAt) }}
                </el-tag>

                <!-- 有效期标签 -->
                <el-tag
                  size="small"
                  :type="getValidityTagType(token.createdAt)"
                  class="validity-tag"
                >
                  <el-icon><Calendar /></el-icon>
                  {{ getValidityStatus(token.createdAt) }}
                </el-tag>

                <!-- 查看用量标签 -->
                <el-tag
                  v-if="token.usage_url"
                  size="small"
                  type="info"
                  class="usage-tag clickable"
                  @click="openUsageUrl(token.usage_url)"
                >
                  <el-icon><TrendCharts /></el-icon>
                  查看用量
                </el-tag>
              </div>
            </div>
            
            <div class="token-actions">
              <el-button
                type="primary"
                text
                @click="toggleTokenDetails(index)"
              >
                <el-icon>
                  <ArrowDown v-if="!expandedTokens.has(index)" />
                  <ArrowUp v-else />
                </el-icon>
              </el-button>
              
              <el-button
                type="danger"
                text
                @click="deleteToken(token)"
              >
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>
          
          <el-collapse-transition>
            <div v-show="expandedTokens.has(index)" class="token-details">
              <div class="detail-row">
                <span class="detail-label">Token:</span>
                <div class="detail-value-container">
                  <span class="detail-value">{{ token.token }}</span>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="DocumentCopy"
                    @click="copyToClipboard(token.token, 'Token')"
                    class="copy-btn"
                  >
                    复制
                  </el-button>
                </div>
              </div>

              <div class="detail-row">
                <span class="detail-label">租户URL:</span>
                <div class="detail-value-container">
                  <span class="detail-value">{{ token.tenant_url }}</span>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="DocumentCopy"
                    @click="copyToClipboard(token.tenant_url, '租户URL')"
                    class="copy-btn"
                  >
                    复制
                  </el-button>
                </div>
              </div>

              <div class="detail-row">
                <span class="detail-label">创建时间:</span>
                <span class="detail-value">{{ formatDetailCreateTime(token.createdAt) }}</span>
              </div>

              <div class="detail-row">
                <span class="detail-label">有效期:</span>
                <div class="detail-value-container">
                  <span class="detail-value" :class="getValidityClass(token.createdAt)">
                    {{ getDetailValidityStatus(token.createdAt) }}
                  </span>
                </div>
              </div>

              <div class="detail-row">
                <span class="detail-label">用量地址:</span>
                <div class="detail-value-container">
                  <el-input
                    v-model="editingUsageUrl[token.token]"
                    placeholder="请输入用量查看地址URL"
                    @blur="updateUsageUrl(token.token, editingUsageUrl[token.token])"
                    @keyup.enter="updateUsageUrl(token.token, editingUsageUrl[token.token])"
                    class="detail-value"
                  />
                  <el-tag
                    v-if="token.usage_url"
                    size="small"
                    type="success"
                    class="usage-tag clickable"
                    @click="openUsageUrl(token.usage_url)"
                  >
                    <el-icon><TrendCharts /></el-icon>
                    查看用量
                  </el-tag>
                </div>
              </div>

              <div v-if="token.in_cool" class="detail-row">
                <span class="detail-label">冷却结束:</span>
                <span class="detail-value">{{ formatCoolEndTime(token.cool_end) }}</span>
              </div>
            </div>
          </el-collapse-transition>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="filteredTokens.length > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="filteredTokens.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑备注对话框 -->
    <el-dialog
      v-model="remarkDialogVisible"
      title="编辑备注"
      width="400px"
      @close="resetRemarkDialog"
    >
      <el-form @submit.prevent="saveRemark">
        <el-form-item label="备注内容">
          <el-input
            v-model="remarkForm.remark"
            maxlength="30"
            show-word-limit
            placeholder="请输入备注（30字以内）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="remarkDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRemark">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  List,
  Search,
  Hide,
  View,
  Refresh,
  Loading,
  DocumentRemove,
  Timer,
  Clock,
  Calendar,
  ArrowDown,
  ArrowUp,
  Delete,
  DocumentCopy,
  TrendCharts
} from '@element-plus/icons-vue'
import { useTokenStore } from '@/stores/tokens'

const tokenStore = useTokenStore()

// 响应式数据
const searchKeyword = ref('')
const tokenVisible = ref(false)
const expandedTokens = ref(new Set())
const currentPage = ref(1)
const pageSize = ref(10)
const showStatusInfo = ref(true)

// 备注编辑相关
const remarkDialogVisible = ref(false)
const remarkForm = reactive({
  token: '',
  remark: ''
})

// 用量地址编辑相关
const editingUsageUrl = ref({})

// 计算属性
const filteredTokens = computed(() => {
  if (!searchKeyword.value) {
    return tokenStore.tokens
  }

  const keyword = searchKeyword.value.toLowerCase()
  return tokenStore.tokens.filter(token =>
    token.token.toLowerCase().includes(keyword) ||
    (token.remark && token.remark.toLowerCase().includes(keyword))
  )
})

const paginatedTokens = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTokens.value.slice(start, end)
})

// 方法
const getTokenNumber = (index) => {
  return (currentPage.value - 1) * pageSize.value + index + 1
}

const toggleTokenVisibility = () => {
  tokenVisible.value = !tokenVisible.value
}

const handleSearch = () => {
  currentPage.value = 1
}

const refreshTokens = async () => {
  const result = await tokenStore.fetchTokens(currentPage.value, pageSize.value)
  if (!result.success) {
    ElMessage.error(result.error || '刷新失败')
  } else {
    // 首次成功加载后隐藏状态提示
    if (tokenStore.tokens.length > 0) {
      showStatusInfo.value = false
    }
  }
}



const toggleTokenDetails = (index) => {
  if (expandedTokens.value.has(index)) {
    expandedTokens.value.delete(index)
  } else {
    expandedTokens.value.add(index)
  }
}

const editRemark = (token) => {
  remarkForm.token = token.token
  remarkForm.remark = token.remark || ''
  remarkDialogVisible.value = true
}

const saveRemark = async () => {
  try {
    const result = await tokenStore.updateTokenRemark(remarkForm.token, remarkForm.remark)
    if (result.success) {
      ElMessage.success('备注更新成功')
      remarkDialogVisible.value = false
    } else {
      ElMessage.error(result.error || '更新备注失败')
    }
  } catch (error) {
    ElMessage.error('更新备注失败')
  }
}

const resetRemarkDialog = () => {
  remarkForm.token = ''
  remarkForm.remark = ''
}

const deleteToken = async (token) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除Token "${token.token.substring(0, 20)}..." 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await tokenStore.deleteToken(token.token)
    if (result.success) {
      ElMessage.success('Token删除成功')
    } else {
      ElMessage.error(result.error || '删除失败')
    }
  } catch (error) {
    // 用户取消操作
  }
}



const formatCoolEndTime = (coolEnd) => {
  if (!coolEnd) return ''
  return new Date(coolEnd).toLocaleString()
}

// 格式化创建时间
const formatCreateTime = (createdAt) => {
  if (!createdAt) return '未知'
  const date = new Date(createdAt)
  const now = new Date()
  const diffTime = now - date
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString()
  }
}

// 获取有效期状态
const getValidityStatus = (createdAt) => {
  if (!createdAt) return '未知'

  const createDate = new Date(createdAt)
  const now = new Date()
  const expireDate = new Date(createDate.getTime() + 7 * 24 * 60 * 60 * 1000) // 7天后

  const diffTime = expireDate - now

  if (diffTime <= 0) {
    return '已过期'
  }

  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60))

  if (diffDays > 0) {
    if (diffHours > 0) {
      return `${diffDays}天${diffHours}小时后过期`
    } else {
      return `${diffDays}天后过期`
    }
  } else if (diffHours > 0) {
    if (diffMinutes > 0) {
      return `${diffHours}小时${diffMinutes}分钟后过期`
    } else {
      return `${diffHours}小时后过期`
    }
  } else if (diffMinutes > 0) {
    return `${diffMinutes}分钟后过期`
  } else {
    return '即将过期'
  }
}

// 获取有效期标签类型
const getValidityTagType = (createdAt) => {
  if (!createdAt) return 'info'

  const createDate = new Date(createdAt)
  const now = new Date()
  const expireDate = new Date(createDate.getTime() + 7 * 24 * 60 * 60 * 1000) // 7天后

  const diffTime = expireDate - now

  if (diffTime <= 0) {
    return 'danger' // 已过期
  }

  const diffHours = Math.floor(diffTime / (1000 * 60 * 60))

  if (diffHours <= 24) {
    return 'danger' // 24小时内过期
  } else if (diffHours <= 72) {
    return 'warning' // 3天内过期
  } else {
    return 'success' // 有效
  }
}

// 格式化详情页面的创建时间
const formatDetailCreateTime = (createdAt) => {
  if (!createdAt) return '未知'
  const date = new Date(createdAt)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取详情页面的有效期状态
const getDetailValidityStatus = (createdAt) => {
  if (!createdAt) return '未知'

  const createDate = new Date(createdAt)
  const now = new Date()
  const expireDate = new Date(createDate.getTime() + 7 * 24 * 60 * 60 * 1000) // 7天后

  const diffTime = expireDate - now

  if (diffTime <= 0) {
    const overTime = Math.abs(diffTime)
    const overDays = Math.floor(overTime / (1000 * 60 * 60 * 24))
    const overHours = Math.floor((overTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (overDays > 0) {
      return `已过期 ${overDays}天${overHours > 0 ? overHours + '小时' : ''}`
    } else if (overHours > 0) {
      return `已过期 ${overHours}小时`
    } else {
      return '刚刚过期'
    }
  }

  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60))

  let statusText = ''
  if (diffDays > 0) {
    statusText = `${diffDays}天`
    if (diffHours > 0) {
      statusText += `${diffHours}小时`
    }
    statusText += '后过期'
  } else if (diffHours > 0) {
    statusText = `${diffHours}小时`
    if (diffMinutes > 0) {
      statusText += `${diffMinutes}分钟`
    }
    statusText += '后过期'
  } else if (diffMinutes > 0) {
    statusText = `${diffMinutes}分钟后过期`
  } else {
    statusText = '即将过期'
  }

  return `${statusText} (${expireDate.toLocaleString('zh-CN')})`
}

// 获取有效期样式类
const getValidityClass = (createdAt) => {
  if (!createdAt) return ''

  const createDate = new Date(createdAt)
  const now = new Date()
  const expireDate = new Date(createDate.getTime() + 7 * 24 * 60 * 60 * 1000) // 7天后

  const diffTime = expireDate - now

  if (diffTime <= 0) {
    return 'validity-expired'
  }

  const diffHours = Math.floor(diffTime / (1000 * 60 * 60))

  if (diffHours <= 24) {
    return 'validity-expired' // 24小时内过期，显示为危险
  } else if (diffHours <= 72) {
    return 'validity-warning' // 3天内过期，显示为警告
  } else {
    return 'validity-valid' // 有效
  }
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 复制到剪贴板
const copyToClipboard = async (text, type) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success(`${type}已复制到剪贴板`)
  } catch (error) {
    // 如果现代API失败，尝试传统方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success(`${type}已复制到剪贴板`)
    } catch (fallbackError) {
      console.error('复制失败:', fallbackError)
      ElMessage.error('复制失败，请手动复制')
    }
  }
}

// 打开用量地址
const openUsageUrl = (url) => {
  if (!url) {
    ElMessage.warning('用量地址为空')
    return
  }

  // 确保URL有协议前缀
  let fullUrl = url
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    fullUrl = 'https://' + url
  }

  window.open(fullUrl, '_blank')
}

// 更新用量地址
const updateUsageUrl = async (token, usageUrl) => {
  if (!usageUrl) {
    usageUrl = ''
  }

  try {
    const result = await tokenStore.updateTokenUsageUrl(token, usageUrl)
    if (result.success) {
      ElMessage.success('用量地址已更新')
    } else {
      ElMessage.error('更新用量地址失败')
    }
  } catch (error) {
    console.error('更新用量地址失败:', error)
    ElMessage.error('更新用量地址失败')
  }
}

// 初始化编辑状态
const initEditingStates = () => {
  editingUsageUrl.value = {}
  tokenStore.tokens.forEach(token => {
    editingUsageUrl.value[token.token] = token.usage_url || ''
  })
}

// 监听tokens变化，更新编辑状态
watch(() => tokenStore.tokens, () => {
  initEditingStates()
}, { deep: true, immediate: true })

// 生命周期
onMounted(() => {
  refreshTokens()
})
</script>

<style scoped lang="scss">
.token-list-container {
  padding: 20px;
}

.status-info {
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 20px;

  @media (max-width: 768px) {
    .toolbar-right {
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}

.token-list-content {
  min-height: 400px;
}

.token-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.token-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

.token-header {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;

  .token-number {
    width: 40px;
    height: 40px;
    background: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
  }

  .token-info {
    flex: 1;
    min-width: 0;

    .token-value {
      font-family: 'Courier New', monospace;
      font-size: 14px;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
      word-break: break-all;
      transition: filter 0.3s ease;

      &.blurred {
        filter: blur(4px);

        &:hover {
          filter: blur(0);
        }
      }
    }

    .token-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;

      .remark-tag {
        cursor: pointer;

        &.empty {
          opacity: 0.6;
        }

        &:hover {
          opacity: 1;
        }
      }

      .cool-tag {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .time-tag {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .validity-tag {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .usage-tag {
        margin-left: 8px;
        display: flex;
        align-items: center;
        gap: 4px;

        &.clickable {
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  .token-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.token-details {
  padding: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-fill-color-extra-light);

  .detail-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    gap: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-label {
      font-weight: 500;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }

    .detail-value-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .detail-value {
        flex: 1;
        word-break: break-all;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        background: var(--el-fill-color-light);
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid var(--el-border-color-lighter);
        color: var(--el-text-color-primary);

        &.validity-expired {
          color: var(--el-color-danger);
          font-weight: 500;
        }

        &.validity-warning {
          color: var(--el-color-warning);
          font-weight: 500;
        }

        &.validity-valid {
          color: var(--el-color-success);
        }
      }

      .copy-btn {
        flex-shrink: 0;
        height: 32px;
        padding: 0 12px;
        font-size: 12px;
      }

      .usage-tag {
        flex-shrink: 0;
        height: 32px;
        padding: 0 12px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;

        &.clickable {
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .token-list-container {
    padding: 12px;
  }

  .token-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .token-info {
      order: 1;

      .token-meta {
        .time-tag,
        .validity-tag {
          font-size: 11px;
          padding: 2px 6px;

          .el-icon {
            font-size: 10px;
          }
        }
      }
    }

    .token-actions {
      order: 2;
      justify-content: flex-end;
    }

    .token-number {
      order: 0;
      align-self: flex-start;
    }
  }

  .token-details {
    .detail-row {
      .detail-value-container {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .copy-btn {
          align-self: flex-end;
          width: fit-content;
        }
      }
    }
  }
}
</style>
