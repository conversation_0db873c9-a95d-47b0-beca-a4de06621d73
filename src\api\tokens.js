import request from '@/utils/request'

export const tokenApi = {
  // 获取token列表
  getTokens(page = 1, pageSize = 10) {
    return request.get('/api/tokens', {
      params: {
        page,
        page_size: pageSize
      }
    })
  },

  // 删除token
  deleteToken(token) {
    return request.delete(`/api/token/${encodeURIComponent(token)}`)
  },

  // 更新token备注
  updateTokenRemark(token, remark) {
    return request.put(`/api/token/${encodeURIComponent(token)}/remark`, {
      remark
    })
  },



  // 批量添加tokens
  addTokens(tokens) {
    return request.post('/api/add/tokens', tokens)
  }
}
