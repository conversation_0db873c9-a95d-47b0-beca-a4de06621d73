import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'

// 导入路由
import oauthRoutes from './routes/oauth.js'

// 导入中间件
import { errorHandler } from './middleware/errorHandler.js'
import { requestLogger } from './middleware/logger.js'

// 加载环境变量
dotenv.config()

// 创建Express应用
const app = express()

// 基础中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}))
app.use(compression())
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    status: 'error',
    error: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
})
app.use(limiter)

// 解析请求体
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// 请求日志
app.use(requestLogger)

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'Token OAuth API',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  })
})

// API路由
app.use('/auth', oauthRoutes)
app.use('/callback', oauthRoutes)

// 根路径信息
app.get('/', (req, res) => {
  res.json({
    service: 'Token OAuth API',
    version: '1.0.0',
    description: '简化的OAuth授权服务，用于Token获取',
    endpoints: {
      health: '/health',
      auth: '/auth',
      callback: '/callback'
    },
    documentation: 'https://github.com/your-repo/token-oauth-api'
  })
})

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    error: '接口不存在',
    path: req.originalUrl,
    method: req.method
  })
})

// 错误处理中间件
app.use(errorHandler)

// 启动服务器
const startServer = () => {
  const PORT = process.env.PORT || 3001
  const HOST = process.env.HOST || 'localhost'
  
  app.listen(PORT, () => {
    console.log('🚀 Token OAuth API 服务启动成功!')
    console.log(`📍 地址: http://${HOST}:${PORT}`)
    console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`)
    console.log(`🔗 CORS: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`)
    console.log(`⏰ 时间: ${new Date().toLocaleString()}`)
    console.log('📋 可用端点:')
    console.log('   GET  /health     - 健康检查')
    console.log('   GET  /auth       - 获取授权URL')
    console.log('   POST /callback   - 授权码换取Token')
  })
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...')
  process.exit(0)
})

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
  process.exit(1)
})

// 启动应用
startServer()

export default app
