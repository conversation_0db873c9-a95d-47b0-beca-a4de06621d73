import { createClient } from '@supabase/supabase-js'
import { settingsUtils } from './database'

// 动态创建Supabase客户端
const createSupabaseClient = async () => {
  try {
    // 从本地设置读取URL和API密钥
    const savedUrl = await settingsUtils.get('supabaseUrl', '')
    const savedKey = await settingsUtils.get('supabaseAnonKey', '')

    // 如果本地没有配置，尝试从环境变量读取
    const supabaseUrl = savedUrl || import.meta.env.VITE_SUPABASE_URL || ''
    const supabaseKey = savedKey || import.meta.env.VITE_SUPABASE_ANON_KEY || ''

    if (!supabaseUrl) {
      throw new Error('Supabase项目URL未配置')
    }

    if (!supabaseKey) {
      throw new Error('Supabase API密钥未配置')
    }

    return createClient(supabaseUrl, supabaseKey)
  } catch (error) {
    console.warn('创建Supabase客户端失败:', error.message)
    return null
  }
}

// 导出默认客户端（用于向后兼容）
export const supabase = await createSupabaseClient()

// Supabase工具函数
export const supabaseUtils = {
  // 上传token到Supabase
  async uploadToken(tokenData) {
    try {
      const client = await createSupabaseClient()
      if (!client) {
        return { success: false, error: 'Supabase客户端未配置' }
      }

      const { data, error } = await client
        .from('tokens')
        .insert([
          {
            token: tokenData.token,
            tenant_url: tokenData.tenant_url,
            remark: tokenData.remark || '',
            usage_url: tokenData.usage_url || '',
            created_at: new Date().toISOString(),
            user_id: tokenData.user_id || null
          }
        ])
        .select()

      if (error) {
        console.error('Supabase上传token失败:', error)
        return { success: false, error: error.message }
      }

      console.log('✅ Token已上传到Supabase:', data)
      return { success: true, data }
    } catch (error) {
      console.error('Supabase上传token异常:', error)
      return { success: false, error: error.message }
    }
  },



  // 从Supabase同步tokens
  async syncTokensFromSupabase() {
    try {
      const client = await createSupabaseClient()
      if (!client) {
        return { success: false, error: 'Supabase客户端未配置' }
      }

      const { data, error } = await client
        .from('tokens')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('从Supabase同步tokens失败:', error)
        return { success: false, error: error.message }
      }

      console.log('✅ 从Supabase同步tokens成功:', data)
      return { success: true, data }
    } catch (error) {
      console.error('从Supabase同步tokens异常:', error)
      return { success: false, error: error.message }
    }
  },

  // 删除Supabase中的token
  async deleteTokenFromSupabase(token) {
    try {
      const client = await createSupabaseClient()
      if (!client) {
        return { success: false, error: 'Supabase客户端未配置' }
      }

      const { error } = await client
        .from('tokens')
        .delete()
        .eq('token', token)

      if (error) {
        console.error('从Supabase删除token失败:', error)
        return { success: false, error: error.message }
      }

      console.log('✅ Token已从Supabase删除')
      return { success: true }
    } catch (error) {
      console.error('从Supabase删除token异常:', error)
      return { success: false, error: error.message }
    }
  },

  // 更新Supabase中的token备注
  async updateTokenRemarkInSupabase(token, remark) {
    try {
      const client = await createSupabaseClient()
      if (!client) {
        return { success: false, error: 'Supabase客户端未配置' }
      }

      const { data, error } = await client
        .from('tokens')
        .update({ remark })
        .eq('token', token)
        .select()

      if (error) {
        console.error('更新Supabase中token备注失败:', error)
        return { success: false, error: error.message }
      }

      console.log('✅ Supabase中token备注已更新:', data)
      return { success: true, data }
    } catch (error) {
      console.error('更新Supabase中token备注异常:', error)
      return { success: false, error: error.message }
    }
  },

  // 更新Supabase中的token用量地址
  async updateTokenUsageUrlInSupabase(token, usageUrl) {
    try {
      const client = await createSupabaseClient()
      if (!client) {
        return { success: false, error: 'Supabase客户端未配置' }
      }

      const { data, error } = await client
        .from('tokens')
        .update({ usage_url: usageUrl })
        .eq('token', token)
        .select()

      if (error) {
        console.error('更新Supabase中token用量地址失败:', error)
        return { success: false, error: error.message }
      }

      console.log('✅ Supabase中token用量地址已更新:', data)
      return { success: true, data }
    } catch (error) {
      console.error('更新Supabase中token用量地址异常:', error)
      return { success: false, error: error.message }
    }
  }
}
