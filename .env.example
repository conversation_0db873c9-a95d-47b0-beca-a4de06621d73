# 服务器配置
PORT=3001
NODE_ENV=development
HOST=localhost

# CORS配置
CORS_ORIGIN=http://localhost:3000

# OAuth配置（基于原Go代码的设置）
# 原Go代码中的clientID = "v"
OAUTH_CLIENT_ID=v
OAUTH_CLIENT_SECRET=
# 原Go代码中redirect_uri为空字符串
OAUTH_REDIRECT_URI=
OAUTH_SCOPE=read write

# 授权服务器配置（基于原Go代码）
# 原Go代码中使用固定的授权服务器
OAUTH_AUTHORIZE_URL=https://auth.augmentcode.com/authorize
OAUTH_TOKEN_ENDPOINT=/token

# 默认租户URL（可选）
DEFAULT_TENANT_URL=https://your-tenant.api.augmentcode.com

# 调试模式
DEBUG=false
