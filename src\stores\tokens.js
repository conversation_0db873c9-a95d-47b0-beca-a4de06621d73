import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { db } from '@/utils/database'
import { supabaseUtils } from '@/utils/supabase'

export const useTokenStore = defineStore('tokens', () => {
  const tokens = ref([])
  const isLoading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const totalPages = ref(0)

  const paginatedTokens = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return tokens.value.slice(start, end)
  })

  // 获取token列表（本地存储 + Supabase同步）
  const fetchTokens = async (page = 1, size = 10) => {
    isLoading.value = true
    try {
      // 优先从本地加载
      await loadTokensFromLocal()
      console.log('✅ 从本地加载token列表成功')

      // 静默尝试从Supabase同步（如果配置了密钥）
      try {
        const supabaseResult = await supabaseUtils.syncTokensFromSupabase()
        if (supabaseResult.success && supabaseResult.data) {
          // 合并Supabase数据到本地
          await syncSupabaseTokensToLocal(supabaseResult.data)
          await loadTokensFromLocal() // 重新加载合并后的数据
          console.log('✅ Supabase同步成功')
        }
      } catch (supabaseError) {
        console.log('ℹ️ Supabase同步跳过 (未配置或不可用)')
      }

      return { success: true }
    } catch (error) {
      console.error('获取token列表失败:', error)
      return { success: false, error: error.message || '加载失败' }
    } finally {
      isLoading.value = false
    }
  }

  // 从本地IndexedDB加载tokens
  const loadTokensFromLocal = async () => {
    try {
      const localTokens = await db.tokens.orderBy('createdAt').reverse().toArray()
      tokens.value = localTokens.map(item => ({
        token: item.token,
        tenant_url: item.tenantUrl,
        remark: item.remark || '',
        usage_url: item.usageUrl || '',
        in_cool: item.inCool || false,
        cool_end: item.coolEnd || null,
        createdAt: item.createdAt || new Date() // 为旧数据设置默认创建时间
      }))
      total.value = tokens.value.length
      totalPages.value = Math.ceil(total.value / pageSize.value)
    } catch (error) {
      console.error('从本地加载tokens失败:', error)
    }
  }



  // 同步Supabase tokens到本地
  const syncSupabaseTokensToLocal = async (supabaseTokens) => {
    try {
      for (const supabaseToken of supabaseTokens) {
        // 检查本地是否已存在该token
        const existingToken = await db.tokens.where('token').equals(supabaseToken.token).first()
        if (!existingToken) {
          // 如果本地不存在，则添加
          await db.tokens.add({
            token: supabaseToken.token,
            tenantUrl: supabaseToken.tenant_url,
            remark: supabaseToken.remark || '',
            usageUrl: supabaseToken.usage_url || '',
            inCool: false,
            coolEnd: null,
            createdAt: new Date(supabaseToken.created_at)
          })
        }
      }
    } catch (error) {
      console.error('同步Supabase tokens到本地失败:', error)
    }
  }

  // 添加token到本地和Supabase
  const addTokenToLocal = async (tokenData) => {
    try {
      // 添加到本地
      await db.tokens.add({
        token: tokenData.token,
        tenantUrl: tokenData.tenant_url,
        remark: '',
        usageUrl: tokenData.usage_url || '',
        inCool: false,
        coolEnd: null,
        createdAt: new Date()
      })

      // 静默上传到Supabase
      try {
        await supabaseUtils.uploadToken(tokenData)
      } catch (supabaseError) {
        console.log('ℹ️ Supabase上传跳过 (未配置或不可用)')
      }
    } catch (error) {
      console.error('添加token失败:', error)
    }
  }

  // 删除token（本地存储 + Supabase）
  const deleteToken = async (token) => {
    try {
      // 从本地删除
      tokens.value = tokens.value.filter(t => t.token !== token)
      total.value = tokens.value.length
      totalPages.value = Math.ceil(total.value / pageSize.value)

      // 从IndexedDB中删除
      await db.tokens.where('token').equals(token).delete()
      console.log('✅ 本地删除token成功')

      // 静默从Supabase删除
      try {
        await supabaseUtils.deleteTokenFromSupabase(token)
      } catch (supabaseError) {
        console.log('ℹ️ Supabase删除跳过 (未配置或不可用)')
      }

      return { success: true }
    } catch (error) {
      console.error('删除token失败:', error)
      return { success: false, error: error.message || '删除失败' }
    }
  }

  // 更新token备注（本地存储 + Supabase）
  const updateTokenRemark = async (token, remark) => {
    try {
      // 更新本地状态
      const tokenIndex = tokens.value.findIndex(t => t.token === token)
      if (tokenIndex !== -1) {
        tokens.value[tokenIndex].remark = remark
      }

      // 更新IndexedDB
      await db.tokens.where('token').equals(token).modify({ remark })
      console.log('✅ 本地更新token备注成功')

      // 静默更新Supabase
      try {
        await supabaseUtils.updateTokenRemarkInSupabase(token, remark)
      } catch (supabaseError) {
        console.log('ℹ️ Supabase更新跳过 (未配置或不可用)')
      }

      return { success: true }
    } catch (error) {
      console.error('更新token备注失败:', error)
      return { success: false, error: error.message || '更新失败' }
    }
  }





  // 更新token用量地址（本地存储 + Supabase）
  const updateTokenUsageUrl = async (token, usageUrl) => {
    try {
      // 更新本地状态
      const tokenIndex = tokens.value.findIndex(t => t.token === token)
      if (tokenIndex !== -1) {
        tokens.value[tokenIndex].usage_url = usageUrl
      }

      // 更新IndexedDB
      await db.tokens.where('token').equals(token).modify({ usageUrl })
      console.log('✅ 本地更新token用量地址成功')

      // 静默更新Supabase
      try {
        await supabaseUtils.updateTokenUsageUrlInSupabase(token, usageUrl)
      } catch (supabaseError) {
        console.log('ℹ️ Supabase更新跳过 (未配置或不可用)')
      }

      return { success: true }
    } catch (error) {
      console.error('更新token用量地址失败:', error)
      return { success: false, error: error.message || '更新失败' }
    }
  }

  return {
    tokens,
    isLoading,
    currentPage,
    pageSize,
    total,
    totalPages,
    paginatedTokens,
    fetchTokens,
    loadTokensFromLocal,
    addTokenToLocal,
    deleteToken,
    updateTokenRemark,
    updateTokenUsageUrl
  }
})
