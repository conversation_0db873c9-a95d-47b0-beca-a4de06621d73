import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { db } from '@/utils/database'

export const useAuthStore = defineStore('auth', () => {
  const isLoading = ref(false)
  const user = ref({ name: '管理员', loginTime: new Date().toISOString() })

  // 始终返回已认证状态
  const isAuthenticated = computed(() => true)

  // 初始化认证状态（简化版）
  const initAuth = async () => {
    try {
      // 设置默认用户信息
      user.value = {
        name: '管理员',
        loginTime: new Date().toISOString()
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
    }
  }

  // 简化的登出功能（主要用于清理本地数据）
  const logout = async () => {
    try {
      // 清除IndexedDB中的认证信息
      await db.auth.clear()
      // 重新设置用户信息
      user.value = {
        name: '管理员',
        loginTime: new Date().toISOString()
      }
    } catch (error) {
      console.error('清除本地认证信息失败:', error)
    }
  }

  return {
    isLoading,
    user,
    isAuthenticated,
    initAuth,
    logout
  }
})
