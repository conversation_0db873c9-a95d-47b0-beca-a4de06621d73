#!/usr/bin/env node

/**
 * 自动配置脚本
 * 根据原Go代码的逻辑自动生成.env文件
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 基于原Go代码的默认配置
const defaultConfig = {
  // 服务器配置
  PORT: '3001',
  NODE_ENV: 'development',
  HOST: 'localhost',
  
  // CORS配置
  CORS_ORIGIN: 'http://localhost:3000',
  
  // OAuth配置（基于原Go代码）
  OAUTH_CLIENT_ID: 'v',  // 原Go代码中的clientID = "v"
  OAUTH_CLIENT_SECRET: '',  // 原Go代码中未使用client_secret
  OAUTH_REDIRECT_URI: '',  // 原Go代码中redirect_uri为空字符串
  OAUTH_SCOPE: 'read write',
  
  // 授权服务器配置（基于原Go代码）
  OAUTH_AUTHORIZE_URL: 'https://auth.augmentcode.com/authorize',
  OAUTH_TOKEN_ENDPOINT: '/token',
  
  // 可选配置
  DEFAULT_TENANT_URL: 'https://your-tenant.api.augmentcode.com',
  DEBUG: 'false'
}

/**
 * 生成.env文件内容
 */
function generateEnvContent(config) {
  const lines = [
    '# Token OAuth API 配置文件',
    '# 基于原Go代码自动生成',
    '',
    '# 服务器配置',
    `PORT=${config.PORT}`,
    `NODE_ENV=${config.NODE_ENV}`,
    `HOST=${config.HOST}`,
    '',
    '# CORS配置',
    `CORS_ORIGIN=${config.CORS_ORIGIN}`,
    '',
    '# OAuth配置（基于原Go代码的设置）',
    '# 原Go代码中的clientID = "v"',
    `OAUTH_CLIENT_ID=${config.OAUTH_CLIENT_ID}`,
    `OAUTH_CLIENT_SECRET=${config.OAUTH_CLIENT_SECRET}`,
    '# 原Go代码中redirect_uri为空字符串',
    `OAUTH_REDIRECT_URI=${config.OAUTH_REDIRECT_URI}`,
    `OAUTH_SCOPE=${config.OAUTH_SCOPE}`,
    '',
    '# 授权服务器配置（基于原Go代码）',
    '# 原Go代码中使用固定的授权服务器',
    `OAUTH_AUTHORIZE_URL=${config.OAUTH_AUTHORIZE_URL}`,
    `OAUTH_TOKEN_ENDPOINT=${config.OAUTH_TOKEN_ENDPOINT}`,
    '',
    '# 默认租户URL（可选）',
    `DEFAULT_TENANT_URL=${config.DEFAULT_TENANT_URL}`,
    '',
    '# 调试模式',
    `DEBUG=${config.DEBUG}`,
    ''
  ]
  
  return lines.join('\n')
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 Token OAuth API 自动配置工具')
  console.log('📋 基于原Go代码逻辑生成配置文件...\n')
  
  const envPath = path.join(__dirname, '.env')
  const envExamplePath = path.join(__dirname, '.env.example')
  
  // 检查是否已存在.env文件
  if (fs.existsSync(envPath)) {
    console.log('⚠️  .env文件已存在')
    console.log('如需重新生成，请先删除现有的.env文件\n')
    return
  }
  
  try {
    // 生成.env文件
    const envContent = generateEnvContent(defaultConfig)
    fs.writeFileSync(envPath, envContent, 'utf8')
    
    console.log('✅ .env文件生成成功!')
    console.log(`📁 位置: ${envPath}\n`)
    
    // 显示配置摘要
    console.log('📋 配置摘要:')
    console.log('----------------------------------------')
    console.log(`服务端口: ${defaultConfig.PORT}`)
    console.log(`CORS源: ${defaultConfig.CORS_ORIGIN}`)
    console.log(`OAuth客户端ID: ${defaultConfig.OAUTH_CLIENT_ID}`)
    console.log(`授权服务器: ${defaultConfig.OAUTH_AUTHORIZE_URL}`)
    console.log('----------------------------------------\n')
    
    console.log('🚀 下一步:')
    console.log('1. 运行 npm install 安装依赖')
    console.log('2. 运行 npm run dev 启动开发服务器')
    console.log('3. 访问 http://localhost:3001/health 验证服务\n')
    
    console.log('💡 提示:')
    console.log('- OAuth配置已根据原Go代码自动设置，通常无需修改')
    console.log('- 如需自定义配置，请编辑生成的.env文件')
    console.log('- 详细说明请参考README.md文件')
    
  } catch (error) {
    console.error('❌ 生成.env文件失败:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()
