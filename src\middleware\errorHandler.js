/**
 * 全局错误处理中间件
 */
export const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  })

  // 默认错误响应
  let status = 500
  let message = '服务器内部错误'

  // 根据错误类型设置响应
  if (err.name === 'ValidationError') {
    status = 400
    message = '请求参数验证失败'
  } else if (err.name === 'UnauthorizedError') {
    status = 401
    message = '未授权访问'
  } else if (err.name === 'ForbiddenError') {
    status = 403
    message = '禁止访问'
  } else if (err.name === 'NotFoundError') {
    status = 404
    message = '资源不存在'
  } else if (err.code === 'ECONNREFUSED') {
    status = 503
    message = '服务暂时不可用'
  } else if (err.code === 'ETIMEDOUT') {
    status = 504
    message = '请求超时'
  }

  // 开发环境返回详细错误信息
  const response = {
    status: 'error',
    error: message
  }

  if (process.env.NODE_ENV === 'development') {
    response.details = {
      message: err.message,
      stack: err.stack
    }
  }

  res.status(status).json(response)
}
