/**
 * 请求日志中间件
 */
export const requestLogger = (req, res, next) => {
  const start = Date.now()
  
  // 记录请求开始
  console.log(`📥 ${req.method} ${req.url} - ${req.ip} - ${new Date().toISOString()}`)
  
  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - start
    const statusColor = getStatusColor(res.statusCode)
    
    console.log(
      `📤 ${req.method} ${req.url} - ${statusColor}${res.statusCode}\x1b[0m - ${duration}ms - ${req.ip}`
    )
  })
  
  next()
}

/**
 * 根据状态码获取颜色
 */
function getStatusColor(statusCode) {
  if (statusCode >= 200 && statusCode < 300) {
    return '\x1b[32m' // 绿色
  } else if (statusCode >= 300 && statusCode < 400) {
    return '\x1b[33m' // 黄色
  } else if (statusCode >= 400 && statusCode < 500) {
    return '\x1b[31m' // 红色
  } else {
    return '\x1b[35m' // 紫色
  }
}
