<template>
  <div class="settings-container">
    <div class="page-title">
      <el-icon class="icon">
        <Setting />
      </el-icon>
      系统设置
    </div>

    <div class="settings-content">
      <!-- Supabase配置 -->
      <div class="settings-section">
        <div class="section-header">
          <h3>Supabase数据库配置</h3>
          <p>配置Supabase数据库连接，实现数据云端同步</p>
        </div>

        <div class="section-content">
          <div class="setting-item">
            <div class="setting-info">
              <h4>API密钥配置</h4>
              <p>输入您的Supabase匿名密钥以启用云端同步功能</p>
            </div>
          </div>

          <el-form :model="supabaseForm" label-width="120px" class="supabase-form">
            <el-form-item label="项目URL">
              <el-input
                v-model="supabaseForm.url"
                placeholder="https://your-project.supabase.co"
              />
              <div class="form-tip">输入您的Supabase项目URL，格式如：https://your-project.supabase.co</div>
            </el-form-item>

            <el-form-item label="匿名密钥">
              <el-input
                v-model="supabaseForm.anonKey"
                type="password"
                placeholder="请输入Supabase匿名密钥"
                show-password
              />
              <div class="form-tip">
                在Supabase项目设置 → API → Project API keys 中找到 anon public 密钥
              </div>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveSupabaseConfig" :loading="savingConfig">
                <el-icon><Check /></el-icon>
                保存配置
              </el-button>
              <el-button @click="testSupabaseConnection" :loading="testingConnection">
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
              <el-button type="success" @click="uploadLocalDataToSupabase" :loading="uploadingData">
                <el-icon><Upload /></el-icon>
                上传本地数据
              </el-button>
            </el-form-item>
          </el-form>

          <div v-if="connectionResult" class="connection-result">
            <el-alert
              :title="connectionResult.success ? '连接成功' : '连接失败'"
              :type="connectionResult.success ? 'success' : 'error'"
              :description="connectionResult.message"
              show-icon
              :closable="false"
            />
          </div>

          <div v-if="uploadResult" class="upload-result">
            <el-alert
              :title="uploadResult.success ? '上传成功' : '上传失败'"
              :type="uploadResult.success ? 'success' : 'error'"
              :description="uploadResult.message"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>

      <!-- 数据管理 -->
      <div class="settings-section">
        <div class="section-header">
          <h3>数据管理</h3>
          <p>管理本地存储的数据</p>
        </div>
        
        <div class="section-content">
          <div class="setting-item">
            <div class="setting-info">
              <h4>数据统计</h4>
              <p>查看本地数据库的存储情况</p>
            </div>
            <div class="setting-action">
              <el-button @click="loadStats" :loading="loadingStats">
                <el-icon><DataAnalysis /></el-icon>
                查看统计
              </el-button>
            </div>
          </div>
          
          <div v-if="dbStats" class="stats-display">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="认证信息">{{ dbStats.authCount }} 条</el-descriptions-item>
              <el-descriptions-item label="Token数量">{{ dbStats.tokensCount }} 条</el-descriptions-item>
              <el-descriptions-item label="设置项">{{ dbStats.settingsCount }} 条</el-descriptions-item>
              <el-descriptions-item label="数据库大小">{{ formatSize(dbStats.dbSize) }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-info">
              <h4>导出数据</h4>
              <p>将所有数据导出为JSON文件</p>
            </div>
            <div class="setting-action">
              <el-button type="primary" @click="exportData" :loading="exporting">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4>导入数据</h4>
              <p>从JSON文件导入数据（会覆盖现有数据）</p>
            </div>
            <div class="setting-action">
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :show-file-list="false"
                accept=".json"
                :on-change="handleFileChange"
              >
                <el-button type="warning">
                  <el-icon><Upload /></el-icon>
                  选择文件
                </el-button>
              </el-upload>
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-info">
              <h4>清空数据</h4>
              <p class="danger-text">删除所有本地存储的数据，此操作不可恢复</p>
            </div>
            <div class="setting-action">
              <el-button type="danger" @click="clearAllData" :loading="clearing">
                <el-icon><Delete /></el-icon>
                清空数据
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用设置 -->
      <div class="settings-section">
        <div class="section-header">
          <h3>应用设置</h3>
          <p>自定义应用的行为和外观</p>
        </div>
        
        <div class="section-content">
          <div class="setting-item">
            <div class="setting-info">
              <h4>主题模式</h4>
              <p>选择应用的主题模式</p>
            </div>
            <div class="setting-action">
              <el-radio-group v-model="themeMode" @change="changeTheme">
                <el-radio label="light">浅色</el-radio>
                <el-radio label="dark">深色</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-info">
              <h4>自动刷新</h4>
              <p>定期自动刷新Token列表</p>
            </div>
            <div class="setting-action">
              <el-switch
                v-model="autoRefresh"
                @change="toggleAutoRefresh"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4>刷新间隔</h4>
              <p>自动刷新的时间间隔（分钟）</p>
            </div>
            <div class="setting-action">
              <el-input-number
                v-model="refreshInterval"
                :min="1"
                :max="60"
                :disabled="!autoRefresh"
                @change="updateRefreshInterval"
              />
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-info">
              <h4>默认隐藏Token</h4>
              <p>默认情况下模糊显示Token内容</p>
            </div>
            <div class="setting-action">
              <el-switch
                v-model="hideTokenByDefault"
                @change="updateHideTokenDefault"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 关于信息 -->
      <div class="settings-section">
        <div class="section-header">
          <h3>关于</h3>
          <p>应用信息和版本</p>
        </div>
        
        <div class="section-content">
          <div class="about-info">
            <h4>Token管理系统</h4>
            <p>版本：v1.0.0</p>
            <p>基于Vue 3 + Element Plus构建</p>
            <p>使用IndexedDB进行本地数据存储</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  DataAnalysis,
  Download,
  Upload,
  Delete,
  Check,
  Connection
} from '@element-plus/icons-vue'
import { dbUtils, settingsUtils, db } from '@/utils/database'

// 响应式数据
const dbStats = ref(null)
const loadingStats = ref(false)
const exporting = ref(false)

// Supabase配置相关
const supabaseForm = ref({
  url: '',
  anonKey: ''
})
const savingConfig = ref(false)
const testingConnection = ref(false)
const connectionResult = ref(null)
const uploadingData = ref(false)
const uploadResult = ref(null)
const clearing = ref(false)
const uploadRef = ref()

// 设置项
const themeMode = ref('light')
const autoRefresh = ref(false)
const refreshInterval = ref(5)
const hideTokenByDefault = ref(false)

// 加载数据库统计信息
const loadStats = async () => {
  loadingStats.value = true
  try {
    dbStats.value = await dbUtils.getStats()
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  } finally {
    loadingStats.value = false
  }
}

// 格式化文件大小
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 导出数据
const exportData = async () => {
  exporting.value = true
  try {
    const data = await dbUtils.exportData()

    // 创建下载链接
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `token-manager-backup-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  } finally {
    exporting.value = false
  }
}

// 处理文件选择
const handleFileChange = (file) => {
  const reader = new FileReader()
  reader.onload = async (e) => {
    try {
      const data = JSON.parse(e.target.result)

      await ElMessageBox.confirm(
        '导入数据将覆盖所有现有数据，确定要继续吗？',
        '确认导入',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await dbUtils.importData(data)
      ElMessage.success('数据导入成功')

      // 刷新统计信息
      await loadStats()
    } catch (error) {
      if (error === 'cancel') return
      console.error('导入数据失败:', error)
      ElMessage.error('导入数据失败：' + error.message)
    }
  }
  reader.readAsText(file.raw)
}

// 清空所有数据
const clearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将删除所有本地数据，包括Token、设置等，且不可恢复。确定要继续吗？',
      '确认清空',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    clearing.value = true
    await dbUtils.clearAll()
    ElMessage.success('数据清空成功')

    // 重置统计信息
    dbStats.value = null
    await loadStats()
  } catch (error) {
    if (error === 'cancel') return
    console.error('清空数据失败:', error)
    ElMessage.error('清空数据失败')
  } finally {
    clearing.value = false
  }
}

// 更改主题
const changeTheme = async (mode) => {
  try {
    await settingsUtils.set('themeMode', mode)

    // 应用主题
    if (mode === 'dark') {
      document.documentElement.classList.add('dark')
    } else if (mode === 'light') {
      document.documentElement.classList.remove('dark')
    } else {
      // auto模式，根据系统设置
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    }

    ElMessage.success('主题设置已保存')
  } catch (error) {
    console.error('保存主题设置失败:', error)
    ElMessage.error('保存主题设置失败')
  }
}

// 切换自动刷新
const toggleAutoRefresh = async (enabled) => {
  try {
    await settingsUtils.set('autoRefresh', enabled)
    ElMessage.success('自动刷新设置已保存')
  } catch (error) {
    console.error('保存自动刷新设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

// 更新刷新间隔
const updateRefreshInterval = async (interval) => {
  try {
    await settingsUtils.set('refreshInterval', interval)
    ElMessage.success('刷新间隔设置已保存')
  } catch (error) {
    console.error('保存刷新间隔设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

// 更新默认隐藏Token设置
const updateHideTokenDefault = async (hide) => {
  try {
    await settingsUtils.set('hideTokenByDefault', hide)
    ElMessage.success('默认隐藏设置已保存')
  } catch (error) {
    console.error('保存默认隐藏设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

// 保存Supabase配置
const saveSupabaseConfig = async () => {
  if (!supabaseForm.value.url.trim()) {
    ElMessage.warning('请输入Supabase项目URL')
    return
  }

  if (!supabaseForm.value.anonKey.trim()) {
    ElMessage.warning('请输入Supabase匿名密钥')
    return
  }

  // 验证URL格式
  try {
    new URL(supabaseForm.value.url)
  } catch (error) {
    ElMessage.warning('请输入有效的URL格式')
    return
  }

  savingConfig.value = true
  try {
    // 保存到本地设置
    await settingsUtils.set('supabaseUrl', supabaseForm.value.url)
    await settingsUtils.set('supabaseAnonKey', supabaseForm.value.anonKey)

    ElMessage.success('Supabase配置已保存，请刷新页面以生效')

    // 清除连接测试结果
    connectionResult.value = null
  } catch (error) {
    console.error('保存Supabase配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    savingConfig.value = false
  }
}

// 测试Supabase连接
const testSupabaseConnection = async () => {
  if (!supabaseForm.value.url.trim()) {
    ElMessage.warning('请先输入Supabase项目URL')
    return
  }

  if (!supabaseForm.value.anonKey.trim()) {
    ElMessage.warning('请先输入Supabase匿名密钥')
    return
  }

  // 验证URL格式
  try {
    new URL(supabaseForm.value.url)
  } catch (error) {
    ElMessage.warning('请输入有效的URL格式')
    return
  }

  testingConnection.value = true
  connectionResult.value = null

  try {
    // 创建临时客户端进行测试
    const { createClient } = await import('@supabase/supabase-js')
    const testClient = createClient(supabaseForm.value.url, supabaseForm.value.anonKey)

    // 尝试查询tokens表
    const { error } = await testClient
      .from('tokens')
      .select('count', { count: 'exact', head: true })

    if (error) {
      connectionResult.value = {
        success: false,
        message: `连接失败: ${error.message}`
      }
    } else {
      connectionResult.value = {
        success: true,
        message: '连接成功！可以正常访问Supabase数据库'
      }
    }
  } catch (error) {
    connectionResult.value = {
      success: false,
      message: `连接异常: ${error.message}`
    }
  } finally {
    testingConnection.value = false
  }
}

// 上传本地数据到Supabase
const uploadLocalDataToSupabase = async () => {
  if (!supabaseForm.value.url.trim() || !supabaseForm.value.anonKey.trim()) {
    ElMessage.warning('请先配置并保存Supabase URL和密钥')
    return
  }

  try {
    await ElMessageBox.confirm(
      '此操作将把所有本地Token数据上传到Supabase数据库。如果Supabase中已存在相同的Token，可能会导致冲突。是否继续？',
      '确认上传',
      {
        confirmButtonText: '确定上传',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    uploadingData.value = true
    uploadResult.value = null

    // 获取本地所有tokens
    const localTokens = await db.tokens.toArray()

    if (localTokens.length === 0) {
      ElMessage.info('本地没有Token数据需要上传')
      return
    }

    // 创建临时客户端
    const { createClient } = await import('@supabase/supabase-js')
    const client = createClient(supabaseForm.value.url, supabaseForm.value.anonKey)

    // 转换数据格式
    const tokensToUpload = localTokens.map(token => {
      let createdAt = new Date().toISOString()

      // 处理不同类型的 createdAt
      if (token.createdAt) {
        if (token.createdAt instanceof Date) {
          createdAt = token.createdAt.toISOString()
        } else if (typeof token.createdAt === 'string') {
          createdAt = token.createdAt
        } else if (typeof token.createdAt === 'number') {
          createdAt = new Date(token.createdAt).toISOString()
        }
      }

      return {
        token: token.token,
        tenant_url: token.tenantUrl,
        remark: token.remark || '',
        usage_url: token.usageUrl || '',
        created_at: createdAt,
        user_id: null
      }
    })

    // 批量上传到Supabase
    const { error } = await client
      .from('tokens')
      .upsert(tokensToUpload, {
        onConflict: 'token',
        ignoreDuplicates: false
      })
      .select()

    if (error) {
      uploadResult.value = {
        success: false,
        message: `上传失败: ${error.message}`
      }
      ElMessage.error('上传失败')
    } else {
      uploadResult.value = {
        success: true,
        message: `成功上传 ${tokensToUpload.length} 个Token到Supabase数据库`
      }
      ElMessage.success(`成功上传 ${tokensToUpload.length} 个Token`)
    }

  } catch (error) {
    if (error === 'cancel') return

    console.error('上传本地数据失败:', error)
    uploadResult.value = {
      success: false,
      message: `上传异常: ${error.message}`
    }
    ElMessage.error('上传失败')
  } finally {
    uploadingData.value = false
  }
}

// 加载设置
const loadSettings = async () => {
  try {
    themeMode.value = await settingsUtils.get('themeMode', 'light')
    autoRefresh.value = await settingsUtils.get('autoRefresh', false)
    refreshInterval.value = await settingsUtils.get('refreshInterval', 5)
    hideTokenByDefault.value = await settingsUtils.get('hideTokenByDefault', false)

    // 加载Supabase配置
    supabaseForm.value.url = await settingsUtils.get('supabaseUrl', '')
    supabaseForm.value.anonKey = await settingsUtils.get('supabaseAnonKey', '')
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadStats()
  loadSettings()
})
</script>

<style scoped lang="scss">
.settings-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.settings-section {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-fill-color-extra-light);

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  p {
    margin: 0;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }
}

.section-content {
  padding: 24px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;

  .setting-info {
    flex: 1;

    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    p {
      margin: 0;
      color: var(--el-text-color-secondary);
      font-size: 14px;
      line-height: 1.5;

      &.danger-text {
        color: var(--el-color-danger);
      }
    }
  }

  .setting-action {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.stats-display {
  margin: 16px 0;
}

.supabase-form {
  margin-top: 16px;

  .form-tip {
    margin-top: 4px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    line-height: 1.4;
  }
}

.connection-result,
.upload-result {
  margin-top: 16px;
}

.about-info {
  h4 {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  p {
    margin: 0 0 8px 0;
    color: var(--el-text-color-secondary);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

@media (max-width: 768px) {
  .settings-container {
    padding: 12px;
  }

  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;

    .setting-action {
      justify-content: flex-start;
    }
  }

  .section-header,
  .section-content {
    padding: 20px;
  }
}
</style>
