import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    children: [
      {
        path: '',
        redirect: '/dashboard/tokens'
      },
      {
        path: 'tokens',
        name: 'TokenList',
        component: () => import('@/views/TokenList.vue')
      },
      {
        path: 'add-token',
        name: 'AddToken',
        component: () => import('@/views/AddToken.vue')
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
