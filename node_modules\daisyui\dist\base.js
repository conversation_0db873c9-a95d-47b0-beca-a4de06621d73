module.exports = {":root,\n[data-theme]":{"backgroundColor":"var(--fallback-b1,oklch(var(--b1)/1))","color":"var(--fallback-bc,oklch(var(--bc)/1))"},"@supports not (color: oklch(0% 0 0))":{":root":{"colorScheme":"light","-FallbackP":"#491eff","-FallbackPc":"#d4dbff","-FallbackS":"#ff41c7","-FallbackSc":"#fff9fc","-FallbackA":"#00cfbd","-FallbackAc":"#00100d","-FallbackN":"#2b3440","-FallbackNc":"#d7dde4","-FallbackB1":"#ffffff","-FallbackB2":"#e5e6e6","-FallbackB3":"#e5e6e6","-FallbackBc":"#1f2937","-FallbackIn":"#00b3f0","-FallbackInc":"#000000","-FallbackSu":"#00ca92","-FallbackSuc":"#000000","-FallbackWa":"#ffc22d","-FallbackWac":"#000000","-FallbackEr":"#ff6f70","-FallbackErc":"#000000"},"@media (prefers-color-scheme: dark)":{":root":{"colorScheme":"dark","-FallbackP":"#7582ff","-FallbackPc":"#050617","-FallbackS":"#ff71cf","-FallbackSc":"#190211","-FallbackA":"#00c7b5","-FallbackAc":"#000e0c","-FallbackN":"#2a323c","-FallbackNc":"#a6adbb","-FallbackB1":"#1d232a","-FallbackB2":"#191e24","-FallbackB3":"#15191e","-FallbackBc":"#a6adbb","-FallbackIn":"#00b3f0","-FallbackInc":"#000000","-FallbackSu":"#00ca92","-FallbackSuc":"#000000","-FallbackWa":"#ffc22d","-FallbackWac":"#000000","-FallbackEr":"#ff6f70","-FallbackErc":"#000000"}}},"html":{"WebkitTapHighlightColor":"transparent"},"*":{"scrollbarColor":"color-mix(in oklch, currentColor 35%, transparent) transparent"},"*:hover":{"scrollbarColor":"color-mix(in oklch, currentColor 60%, transparent) transparent"}};