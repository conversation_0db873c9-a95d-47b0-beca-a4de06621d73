#!/usr/bin/env node

/**
 * 诊断脚本 - 检查API连接状态
 */

import axios from 'axios'

const services = [
  {
    name: 'Node.js OAuth API',
    url: 'http://localhost:3001/health',
    description: 'OAuth授权服务'
  },
  {
    name: 'Go后端API',
    url: 'http://localhost:27080/health',
    description: '原Go后端服务（可选）'
  }
]

async function checkService(service) {
  try {
    const response = await axios.get(service.url, { timeout: 3000 })
    return {
      ...service,
      status: 'online',
      data: response.data
    }
  } catch (error) {
    return {
      ...service,
      status: 'offline',
      error: error.message
    }
  }
}

async function diagnose() {
  console.log('🔍 Token管理系统 - 服务诊断')
  console.log('=' .repeat(50))
  
  const results = await Promise.all(services.map(checkService))
  
  results.forEach(result => {
    console.log(`\n📋 ${result.name}`)
    console.log(`   描述: ${result.description}`)
    console.log(`   地址: ${result.url}`)
    console.log(`   状态: ${result.status === 'online' ? '🟢 在线' : '🔴 离线'}`)
    
    if (result.status === 'online') {
      console.log(`   版本: ${result.data.version || 'N/A'}`)
      console.log(`   服务: ${result.data.service || result.data.status || 'N/A'}`)
    } else {
      console.log(`   错误: ${result.error}`)
    }
  })
  
  console.log('\n' + '=' .repeat(50))
  console.log('💡 诊断结果分析:')
  
  const oauthApi = results.find(r => r.name === 'Node.js OAuth API')
  const goApi = results.find(r => r.name === 'Go后端API')
  
  if (oauthApi.status === 'online' && goApi.status === 'offline') {
    console.log('✅ 推荐配置: 仅使用OAuth API + 本地存储')
    console.log('   - OAuth功能: Node.js API (端口3001)')
    console.log('   - Token管理: 本地IndexedDB')
    console.log('   - 无需Go后端服务')
  } else if (oauthApi.status === 'online' && goApi.status === 'online') {
    console.log('✅ 混合配置: OAuth API + Go后端')
    console.log('   - OAuth功能: Node.js API (端口3001)')
    console.log('   - Token管理: Go后端API (端口27080)')
    console.log('   - 完整功能支持')
  } else if (oauthApi.status === 'offline' && goApi.status === 'online') {
    console.log('⚠️  传统配置: 仅Go后端')
    console.log('   - 需要启动Node.js OAuth API获得最佳体验')
    console.log('   - 或使用原Go后端的OAuth功能')
  } else {
    console.log('❌ 无服务运行')
    console.log('   - 请启动至少一个后端服务')
  }
  
  console.log('\n🚀 启动命令:')
  console.log('   Node.js OAuth API: cd token-oauth-api && npm run dev')
  console.log('   Go后端API: 运行原Go程序')
  console.log('   前端应用: cd vue-token-manager && npm run dev')
  
  console.log('\n📖 更多信息请参考README.md文件')
}

diagnose().catch(console.error)
