import express from 'express'
import crypto from 'crypto'
import axios from 'axios'

const router = express.Router()

// 存储OAuth状态（生产环境建议使用Redis）
const oauthStates = new Map()

// 清理过期的OAuth状态
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of oauthStates.entries()) {
    if (now - value.timestamp > 10 * 60 * 1000) { // 10分钟过期
      oauthStates.delete(key)
    }
  }
}, 5 * 60 * 1000) // 每5分钟清理一次

/**
 * 生成OAuth状态参数
 */
function generateOAuthState() {
  const state = crypto.randomBytes(16).toString('hex')
  const codeVerifier = crypto.randomBytes(32).toString('base64url')
  const codeChallenge = crypto
    .createHash('sha256')
    .update(codeVerifier)
    .digest('base64url')
  
  return {
    state,
    codeVerifier,
    codeChallenge
  }
}

/**
 * 构建授权URL（基于原Go代码逻辑）
 */
function buildAuthorizeURL(oauthParams) {
  // 使用固定的授权服务器地址前缀
  const authUrl = new URL(process.env.OAUTH_AUTHORIZE_URL || 'https://auth.augmentcode.com/authorize')

  // 按照原Go代码的参数设置
  authUrl.searchParams.set('response_type', 'code')
  authUrl.searchParams.set('code_challenge', oauthParams.codeChallenge)
  authUrl.searchParams.set('client_id', process.env.OAUTH_CLIENT_ID || 'v')
  authUrl.searchParams.set('state', oauthParams.state)
  authUrl.searchParams.set('prompt', 'login')

  return authUrl.toString()
}

/**
 * GET /auth - 获取授权URL（基于原Go代码逻辑）
 */
router.get('/', (_, res) => {
  try {
    // 生成OAuth参数
    const oauthParams = generateOAuthState()

    // 存储OAuth状态（简化版，不需要租户URL）
    oauthStates.set(oauthParams.state, {
      codeVerifier: oauthParams.codeVerifier,
      timestamp: Date.now()
    })

    // 构建授权URL（使用固定的授权服务器）
    const authorizeUrl = buildAuthorizeURL(oauthParams)

    res.json({
      status: 'success',
      authorize_url: authorizeUrl,
      state: oauthParams.state
    })

  } catch (error) {
    console.error('获取授权URL失败:', error)
    res.status(500).json({
      status: 'error',
      error: '生成授权URL失败: ' + error.message
    })
  }
})

/**
 * POST /callback - 授权码换取Token（基于原Go代码逻辑）
 */
router.post('/', async (req, res) => {
  try {
    const { code, state, tenant_url } = req.body

    // 验证必需参数
    if (!code) {
      return res.status(400).json({
        status: 'error',
        error: '缺少授权码参数'
      })
    }

    if (!state) {
      return res.status(400).json({
        status: 'error',
        error: '缺少状态参数'
      })
    }

    if (!tenant_url) {
      return res.status(400).json({
        status: 'error',
        error: '缺少租户URL参数'
      })
    }

    // 验证OAuth状态
    const storedState = oauthStates.get(state)
    if (!storedState) {
      return res.status(400).json({
        status: 'error',
        error: '无效的状态参数或状态已过期'
      })
    }
    
    // 构建token请求（基于原Go代码逻辑）
    const tokenUrl = `${tenant_url.endsWith('/') ? tenant_url : tenant_url + '/'}token`

    const tokenData = {
      grant_type: 'authorization_code',
      client_id: process.env.OAUTH_CLIENT_ID || 'v',
      code_verifier: storedState.codeVerifier,
      redirect_uri: process.env.OAUTH_REDIRECT_URI || '',
      code: code
    }

    // 发送token请求（使用JSON格式，与原Go代码一致）
    const response = await axios.post(tokenUrl, tokenData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 10000 // 10秒超时
    })
    
    // 清理已使用的OAuth状态
    oauthStates.delete(state)
    
    // 验证响应
    if (!response.data.access_token) {
      return res.status(400).json({
        status: 'error',
        error: '授权服务器返回的响应中没有访问令牌'
      })
    }
    
    // 返回成功响应
    res.json({
      status: 'success',
      token: response.data.access_token,
      token_type: response.data.token_type || 'Bearer',
      expires_in: response.data.expires_in,
      scope: response.data.scope,
      tenant_url: tenant_url
    })
    
  } catch (error) {
    console.error('授权码换取Token失败:', error)
    
    // 处理不同类型的错误
    if (error.response) {
      // HTTP错误响应
      const status = error.response.status
      const data = error.response.data
      
      res.status(400).json({
        status: 'error',
        error: `授权服务器错误 (${status}): ${data.error_description || data.error || '未知错误'}`,
        details: data
      })
    } else if (error.request) {
      // 网络错误
      res.status(500).json({
        status: 'error',
        error: '无法连接到授权服务器，请检查网络连接和租户URL'
      })
    } else {
      // 其他错误
      res.status(500).json({
        status: 'error',
        error: '处理授权请求时发生错误: ' + error.message
      })
    }
  }
})

export default router
